#!/usr/bin/env python3

"""Test script for gripper functionality in MIRA framework."""

import os
import sys
import numpy as np

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

from ravens import tasks
from ravens.environments.environment import Environment


def test_gripper_basic():
    """Test basic gripper functionality."""
    print("Testing basic gripper functionality...")
    
    # Initialize environment and task
    assets_root = './ravens/environments/assets/'
    env = Environment(assets_root, disp=True, hz=240)
    
    # Use gripper test task
    task = tasks.names['gripper-test']()
    task.mode = 'test'
    env.set_task(task)
    
    # Reset environment
    obs, info = env.reset()
    print("Environment reset successfully")
    
    # Get oracle agent
    agent = task.oracle(env)
    
    # Execute one action
    try:
        act = agent.act(obs, info)
        if act is not None:
            print(f"Oracle action: {act}")
            obs, reward, done, info = env.step(act)
            print(f"Reward: {reward}, Done: {done}")
        else:
            print("No action returned by oracle")
    except Exception as e:
        print(f"Error during action execution: {e}")
    
    # Close environment
    env.close()
    print("Test completed")


def test_gripper_pick_place():
    """Test gripper pick and place functionality."""
    print("Testing gripper pick and place...")
    
    # Initialize environment and task
    assets_root = './ravens/environments/assets/'
    env = Environment(assets_root, disp=True, hz=240)
    
    # Use gripper pick-place task
    task = tasks.names['gripper-pick-place']()
    task.mode = 'test'
    env.set_task(task)
    
    # Reset environment
    obs, info = env.reset()
    print("Environment reset successfully")
    
    # Get oracle agent
    agent = task.oracle(env)
    
    # Execute multiple actions
    max_steps = 5
    step = 0
    total_reward = 0
    
    while step < max_steps and not task.done():
        try:
            act = agent.act(obs, info)
            if act is not None:
                print(f"Step {step}: Oracle action: {act}")
                obs, reward, done, info = env.step(act)
                total_reward += reward
                print(f"Step {step}: Reward: {reward}, Total: {total_reward}, Done: {done}")
                step += 1
            else:
                print("No action returned by oracle")
                break
        except Exception as e:
            print(f"Error during action execution: {e}")
            break
    
    print(f"Final total reward: {total_reward}")
    
    # Close environment
    env.close()
    print("Test completed")


def test_environment_with_gripper():
    """Test environment initialization with gripper."""
    print("Testing environment initialization with gripper...")
    
    try:
        # Initialize environment and task
        assets_root = './ravens/environments/assets/'
        env = Environment(assets_root, disp=False, hz=240)
        
        # Use gripper test task
        task = tasks.names['gripper-test']()
        task.mode = 'test'
        env.set_task(task)
        
        # Reset environment
        obs, info = env.reset()
        print("✓ Environment reset successfully")
        
        # Check if gripper is properly initialized
        if hasattr(env, 'ee') and env.ee is not None:
            print("✓ End effector (gripper) initialized")
            print(f"  End effector type: {type(env.ee)}")
            print(f"  Gripper activated: {env.ee.activated}")
        else:
            print("✗ End effector not found")
        
        # Close environment
        env.close()
        print("✓ Test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("MIRA Gripper Integration Test")
    print("=" * 40)
    
    # Test 1: Basic environment initialization
    success = test_environment_with_gripper()
    
    if success:
        print("\n" + "=" * 40)
        print("Running interactive tests...")
        
        # Test 2: Basic gripper functionality
        try:
            test_gripper_basic()
        except KeyboardInterrupt:
            print("\nTest interrupted by user")
        except Exception as e:
            print(f"Basic test failed: {e}")
        
        # Test 3: Pick and place functionality
        try:
            test_gripper_pick_place()
        except KeyboardInterrupt:
            print("\nTest interrupted by user")
        except Exception as e:
            print(f"Pick and place test failed: {e}")
    
    print("\nAll tests completed!")
