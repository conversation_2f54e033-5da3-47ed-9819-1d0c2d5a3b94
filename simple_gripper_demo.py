#!/usr/bin/env python3

"""Simple gripper demonstration: place a block and try to grasp it."""

import os
import sys
import time
import numpy as np

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

import pybullet as p
from ravens.tasks.grippers import RobotiqGripper
from ravens.utils import pybullet_utils


def simple_gripper_grasp_demo():
    """Demonstrate placing a block and grasping it with the gripper."""
    print("Starting simple gripper grasp demonstration...")
    print("This demo will:")
    print("1. Load a UR5 robot with gripper")
    print("2. Place a simple block")
    print("3. Move robot to grasp position")
    print("4. Attempt to grasp the block")
    print("5. Move the grasped block")
    print("6. Release the block")
    
    # Start PyBullet with GUI
    client = p.connect(p.GUI)
    p.setGravity(0, 0, -9.8)
    p.setRealTimeSimulation(0)
    
    # Set camera position for better view
    p.resetDebugVisualizerCamera(
        cameraDistance=1.2,
        cameraYaw=45,
        cameraPitch=-30,
        cameraTargetPosition=[0.5, 0, 0.2])
    
    try:
        assets_root = './ravens/environments/assets/'
        p.setAdditionalSearchPath(assets_root)
        
        # Load plane
        plane_id = p.loadURDF("plane/plane.urdf", [0, 0, -0.001])
        print("✓ Loaded ground plane")
        
        # Load UR5 robot
        ur5_id = p.loadURDF("ur5/ur5.urdf", [0, 0, 0])
        print(f"✓ Loaded UR5 robot with ID: {ur5_id}")
        
        # Set robot to home position
        home_joints = np.array([-1, -0.5, 0.5, -0.5, -0.5, 0]) * np.pi
        for i, joint_pos in enumerate(home_joints):
            p.resetJointState(ur5_id, i, joint_pos)
        print("✓ Set robot to home position")
        
        # Create obj_ids and add a block
        obj_ids = {'rigid': [], 'fixed': [], 'deformable': []}
        
        # Add a single block at a known position (closer to robot)
        block_pos = [0.45, 0.0, 0.02]  # Closer to robot for easier grasping
        block_id = p.loadURDF("block/block.urdf", block_pos)
        obj_ids['rigid'].append(block_id)

        # Color the block red for visibility
        p.changeVisualShape(block_id, -1, rgbaColor=[1.0, 0.2, 0.2, 1.0])
        print(f"✓ Added block at position {block_pos}")

        # Initialize gripper
        gripper = RobotiqGripper(assets_root, ur5_id, 9, obj_ids)
        print(f"✓ Initialized gripper with body ID: {gripper.body}")

        # Step 1: Show initial state
        print("\n1. Initial state - robot and block")
        gripper._open_gripper()
        for _ in range(60):  # 1 second
            p.stepSimulation()
            time.sleep(1./60.)

        # Step 2: Move robot to pre-grasp position
        print("2. Moving robot to pre-grasp position...")

        # Define joint positions to move end effector above the block (adjusted for closer position)
        pre_grasp_joints = np.array([-1.0, -1.3, 1.5, -1.7, -1.5, 0])

        # Smooth movement to pre-grasp position
        current_joints = np.array([p.getJointState(ur5_id, i)[0] for i in range(6)])

        for step in range(120):  # 2 seconds of movement
            alpha = step / 119.0
            interpolated_joints = current_joints + alpha * (pre_grasp_joints - current_joints)

            for i, joint_pos in enumerate(interpolated_joints):
                p.setJointMotorControl2(
                    ur5_id, i, p.POSITION_CONTROL,
                    targetPosition=joint_pos, force=500)

            p.stepSimulation()
            time.sleep(1./60.)

        print("✓ Moved to pre-grasp position")

        # Step 3: Lower robot to grasp position
        print("3. Lowering robot to grasp position...")

        grasp_joints = np.array([-1.0, -0.9, 1.1, -1.7, -1.5, 0])  # Lower position for grasping
        current_joints = np.array([p.getJointState(ur5_id, i)[0] for i in range(6)])

        for step in range(90):  # 1.5 seconds
            alpha = step / 89.0
            interpolated_joints = current_joints + alpha * (grasp_joints - current_joints)

            for i, joint_pos in enumerate(interpolated_joints):
                p.setJointMotorControl2(
                    ur5_id, i, p.POSITION_CONTROL,
                    targetPosition=joint_pos, force=500)

            p.stepSimulation()
            time.sleep(1./60.)

        print("✓ Moved to grasp position")

        # Check gripper position relative to block
        gripper_pos = p.getBasePositionAndOrientation(gripper.body)[0]
        block_pos_actual = p.getBasePositionAndOrientation(block_id)[0]
        distance = np.linalg.norm(np.array(gripper_pos) - np.array(block_pos_actual))
        print(f"   Distance between gripper and block: {distance:.3f}m")
        
        # Step 4: Close gripper and attempt grasp
        print("4. Closing gripper and attempting grasp...")
        
        gripper._close_gripper()
        for _ in range(60):  # 1 second
            p.stepSimulation()
            time.sleep(1./60.)
        
        # Try to activate gripper (create constraints if in contact)
        gripper.activate()
        print(f"   Gripper activated: {gripper.activated}")
        
        if gripper.activated:
            print("   ✓ Successfully grasped the block!")
            
            # Step 5: Lift the block
            print("5. Lifting the grasped block...")
            
            lift_joints = np.array([-1.2, -1.4, 1.6, -1.7, -1.5, 0])
            current_joints = np.array([p.getJointState(ur5_id, i)[0] for i in range(6)])
            
            for step in range(90):  # 1.5 seconds
                alpha = step / 89.0
                interpolated_joints = current_joints + alpha * (lift_joints - current_joints)
                
                for i, joint_pos in enumerate(interpolated_joints):
                    p.setJointMotorControl2(
                        ur5_id, i, p.POSITION_CONTROL, 
                        targetPosition=joint_pos, force=500)
                
                p.stepSimulation()
                time.sleep(1./60.)
            
            print("   ✓ Lifted the block")
            
            # Step 6: Move to a different position
            print("6. Moving block to new position...")
            
            move_joints = np.array([-0.8, -1.4, 1.6, -1.7, -1.5, 0])
            current_joints = np.array([p.getJointState(ur5_id, i)[0] for i in range(6)])
            
            for step in range(120):  # 2 seconds
                alpha = step / 119.0
                interpolated_joints = current_joints + alpha * (move_joints - current_joints)
                
                for i, joint_pos in enumerate(interpolated_joints):
                    p.setJointMotorControl2(
                        ur5_id, i, p.POSITION_CONTROL, 
                        targetPosition=joint_pos, force=500)
                
                p.stepSimulation()
                time.sleep(1./60.)
            
            print("   ✓ Moved block to new position")
            
            # Step 7: Release the block
            print("7. Releasing the block...")
            gripper.release()
            print(f"   Gripper activated after release: {gripper.activated}")
            
            # Let the block fall
            for _ in range(120):  # 2 seconds
                p.stepSimulation()
                time.sleep(1./60.)
            
            print("   ✓ Released the block")
            
        else:
            print("   ✗ Failed to grasp the block")
            print("   This might happen if the gripper is not close enough to the block")
        
        print("\n🎉 Demonstration completed!")
        print("The gripper successfully demonstrated:")
        print("- Opening and closing")
        print("- Moving to target positions")
        if gripper.activated or gripper.check_grasp():
            print("- Grasping objects")
            print("- Moving grasped objects")
            print("- Releasing objects")
        
        print("\nPress Enter to close the simulation...")
        input()
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        p.disconnect()


if __name__ == '__main__':
    print("MIRA Simple Gripper Grasp Demo")
    print("=" * 50)
    
    print("\nThis demo will show a complete pick-and-place operation using the gripper.")
    print("The robot will:")
    print("- Move to a block")
    print("- Grasp it with the gripper")
    print("- Move it to a new location")
    print("- Release it")
    
    input("\nPress Enter to start the demo...")
    
    try:
        simple_gripper_grasp_demo()
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"Demo failed: {e}")
    
    print("\nDemo completed!")
