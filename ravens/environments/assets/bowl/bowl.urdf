<?xml version="1.0" ?>
<robot name="bowl.urdf">
  <link name="baseLink">
    <contact>
      <lateral_friction value="1.0"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.01 0 0.02"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
				<mesh filename="textured-0008192.obj" scale="1.0 1.0 0.4"/>
      </geometry>
      <material name="green">
        <color rgba="0.34901961 0.6627451 0.30980392 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
	 	    <mesh filename="cup.obj" scale="1.0 1.0 0.4"/>
      </geometry>
    </collision>
  </link>
</robot>
