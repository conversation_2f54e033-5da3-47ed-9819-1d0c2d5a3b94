<?xml version="0.0" ?>
<robot name="container.urdf">
  <link name="baseLink">
    <contact>
      <lateral_friction value="0.3"/>
      <rolling_friction value="0.0001"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>

    <visual>
      <origin rpy="0 0 0" xyz="0 0 -HALF2"/>
      <geometry>
        <box size="DIM0 DIM1 .002"/>
      </geometry>
      <material name="brown">
        <color rgba="0.61176471 0.45882353 0.37254902 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -HALF2"/>
      <geometry>
        <box size="DIM0 DIM1 .002"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="0 -HALF1 0"/>
      <geometry>
        <box size="DIM0 .002 DIM2"/>
      </geometry>
      <material name="brown">
        <color rgba="0.61176471 0.45882353 0.37254902 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 -HALF1 0"/>
      <geometry>
        <box size="DIM0 .002 DIM2"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="0 HALF1 0"/>
      <geometry>
        <box size="DIM0 .002 DIM2"/>
      </geometry>
      <material name="brown">
        <color rgba="0.61176471 0.45882353 0.37254902 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 HALF1 0"/>
      <geometry>
        <box size="DIM0 .002 DIM2"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="-HALF0 0 0"/>
      <geometry>
        <box size=".002 DIM1 DIM2"/>
      </geometry>
      <material name="brown">
        <color rgba="0.61176471 0.45882353 0.37254902 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="-HALF0 0 0"/>
      <geometry>
        <box size=".002 DIM1 DIM2"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="HALF0 0 0"/>
      <geometry>
        <box size=".002 DIM1 DIM2"/>
      </geometry>
      <material name="brown">
        <color rgba="0.61176471 0.45882353 0.37254902 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="HALF0 0 0"/>
      <geometry>
        <box size=".002 DIM1 DIM2"/>
      </geometry>
    </collision>

  </link>
</robot>

