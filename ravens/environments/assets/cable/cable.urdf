<?xml version="1.0" ?>
<!-- ============================================================================================= -->
<!-- |    This document was autogenerated by xacro from ur_description/urdf/ur5_robot.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                           | -->
<!-- ============================================================================================= -->
<robot name="ur5" xmlns:xacro="http://ros.org/wiki/xacro">
  <material name="red">
    <color rgba="1 0.3412 0.3490 1"/>
  </material>

  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.04 0.04 0.04"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.04 0.04 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="4.0"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <inertia ixx="0.00443333156" ixy="0.0" ixz="0.0" iyy="0.00443333156" iyz="0.0" izz="0.0072"/>
    </inertial>
  </link>

  <joint name="shoulder_pan_joint" type="spherical">
    <parent link="base_link"/>
    <child link="shoulder_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.089159"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-6.28318530718" upper="6.28318530718" velocity="3.15"/>
    <dynamics damping="1.0" friction="0.0"/>
  </joint>

  <link name="shoulder_link">
    <visual>
      <geometry>
        <box size="0.04 0.04 0.04"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.04 0.04 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="3.7"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <inertia ixx="0.010267495893" ixy="0.0" ixz="0.0" iyy="0.010267495893" iyz="0.0" izz="0.00666"/>
    </inertial>
  </link>

  <!-- <joint name="shoulder_lift_joint" type="revolute">
    <parent link="shoulder_link"/>
    <child link="upper_arm_link"/>
    <origin rpy="0.0 1.57079632679 0.0" xyz="0.0 0.13585 0.0"/>
    <axis xyz="0 1 0"/>
    <limit effort="150.0" lower="-6.28318530718" upper="6.28318530718" velocity="3.15"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint> -->
</robot>
