<?xml version="0.0" ?>
<robot name="cube.urdf">
  <link name="baseLink">
    <contact>
      <lateral_friction value="1.0"/>
      <rolling_friction value="0.0001"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="pallet.obj" scale="0.25 0.25 0.25"/>
      </geometry>
      <material name="light wood yellow">
        <color rgba="0.8706, 0.7216, 0.5294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="pallet.obj" scale="0.25 0.25 0.25"/>
      </geometry>
    </collision>
  </link>
</robot>

