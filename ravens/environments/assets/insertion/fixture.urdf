<?xml version="0.0" ?>
<robot name="ell.urdf">
  <link name="baseLink">
    <contact>
      <lateral_friction value="0.3"/>
      <rolling_friction value="0.0001"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>

    <visual>
      <origin rpy="0 0 0" xyz="0.025 -0.025 0"/>
      <geometry>
        <box size=".1 .002 .04"/>
      </geometry>
      <material name="gray">
        <!-- <color rgba="0 1 0 1"/> -->
        <color rgba="0.7294117647058823, 0.6901960784313725, 0.6745098039215687 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025 -0.025 0"/>
      <geometry>
        <box size=".1 .002 .04"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="0 0.075 0"/>
      <geometry>
        <box size=".05 .002 .04"/>
      </geometry>
      <material name="gray">
        <!-- <color rgba="0 1 0 1"/> -->
        <color rgba="0.7294117647058823, 0.6901960784313725, 0.6745098039215687 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0.075 0"/>
      <geometry>
        <box size=".05 .002 .04"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="0.05 0.025 0"/>
      <geometry>
        <box size=".05 .002 .04"/>
      </geometry>
      <material name="gray">
        <!-- <color rgba="0 1 0 1"/> -->
        <color rgba="0.7294117647058823, 0.6901960784313725, 0.6745098039215687 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.05 0.025 0"/>
      <geometry>
        <box size=".05 .002 .04"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="-0.025 0.025 0"/>
      <geometry>
        <box size=".002 .105 .04"/>
      </geometry>
      <material name="gray">
        <!-- <color rgba="0 1 0 1"/> -->
        <color rgba="0.7294117647058823, 0.6901960784313725, 0.6745098039215687 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="-0.025 0.025 0"/>
      <geometry>
        <box size=".002 .105 .04"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="0.025 0.05 0"/>
      <geometry>
        <box size=".002 .055 .04"/>
      </geometry>
      <material name="gray">
        <!-- <color rgba="0 1 0 1"/> -->
        <color rgba="0.7294117647058823, 0.6901960784313725, 0.6745098039215687 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025 0.05 0"/>
      <geometry>
        <box size=".002 .055 .04"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="0.075 0 0"/>
      <geometry>
        <box size=".002 .055 .04"/>
      </geometry>
      <material name="gray">
        <!-- <color rgba="0 1 0 1"/> -->
        <color rgba="0.7294117647058823, 0.6901960784313725, 0.6745098039215687 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.075 0 0"/>
      <geometry>
        <box size=".002 .055 .04"/>
      </geometry>
    </collision>

    <visual>
      <origin rpy="0 0 0" xyz="0.075 0 0"/>
      <geometry>
        <box size=".002 .055 .04"/>
      </geometry>
      <material name="gray">
        <!-- <color rgba="0 1 0 1"/> -->
        <color rgba="0.7294117647058823, 0.6901960784313725, 0.6745098039215687 1"/>
      </material>
    </visual>

    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.02"/>
      <geometry>
        <box size=".02 .02 .001"/>
      </geometry>
    </collision>

  </link>
</robot>
