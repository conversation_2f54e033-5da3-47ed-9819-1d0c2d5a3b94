<?xml version="1.0" ?>
<robot name="object.urdf">
  <link name="baseLink">
    <contact>
      <lateral_friction value="2.0"/>
      <rolling_friction value="0.0001"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
         <mesh filename="FNAME0" scale="SCALE0 SCALE1 SCALE2"/>
      </geometry>
      <material name="red">
        <color rgba="COLOR0 COLOR1 COLOR2 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="FNAME0" scale="SCALE0 SCALE1 SCALE2"/>
      </geometry>
    </collision>
  </link>
</robot>
