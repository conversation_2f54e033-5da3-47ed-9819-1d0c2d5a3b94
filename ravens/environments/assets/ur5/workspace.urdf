<?xml version="1.0" ?>
<robot name="workspace.urdf">
  <link name="baseLink">
    <contact>
      <lateral_friction value="1.0"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="plane.obj" scale="0.05 0.15 1"/>
      </geometry>
      <material name="DarkGrey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
    <!-- <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.005"/>
      <geometry>
        <box size="2.0 3.0 0.01"/>
      </geometry>
    </collision> -->
  </link>
</robot>

