Original work Copyright 2018 ROS Industrial (https://rosindustrial.org/)
Modified work Copyright 2018 Virtana, Inc (www.virtanatech.com)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
or implied. See the License for the specific language governing
permissions and limitations under the License.

Changenotes:

2018-05-08: <PERSON> (<EMAIL>)
The visual/ mesh files were generated from the dae files in the
ros-industrial universal robot repo [1]. Since the collada pyBullet
parser is somewhat limited, it is unable to parse the UR collada mesh
files.  Thus, we imported these collada files into blender and
converted them into STL files.  We lost material definitions during
the conversion, but that's ok.

The URDF was generated by running the xacro xml preprocessor on the
URDF included in the ur_description repo already mentioned here.
Additional manual tweaking was required to update resource paths and
to remove errors caused by missing inertia elements.  Varios Gazebo
plugin tags were also removed.

[1] - https://github.com/ros-industrial/universal_robot/tree/kinetic-devel/ur_description/meshes/ur5/visual
