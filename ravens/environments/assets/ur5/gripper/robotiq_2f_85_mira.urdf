<?xml version="1.0" ?>
<!-- Simplified Robotiq 2F-85 gripper for MIRA framework -->
<robot name="robotiq_2f_85_mira" xmlns:xacro="http://ros.org/wiki/xacro">
  <material name="<PERSON>">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  
  <!-- Base link -->
  <link name="base_link">
    <origin xyz="0 0 0"/>
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
  </link>
  
  <!-- Gripper base -->
  <joint name="base_link_robotiq_2f_85_base_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="base_link"/>
    <child link="robotiq_2f_85_base"/>
  </joint>
  
  <link name="robotiq_2f_85_base">
    <inertial>
      <mass value="0.5"/>
      <origin xyz="0 0 0.055"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="robotiq-2f-base.obj" scale="0.1 0.1 0.1"/>
      </geometry>
      <material name="Blanc"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="robotiq-2f-base.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  
  <!-- Right finger -->
  <joint name="robotiq_2f_85_right_driver_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.0306011 0.054904"/>
    <parent link="robotiq_2f_85_base"/>
    <child link="robotiq_2f_85_right_driver"/>
    <axis xyz="1 0 0"/>
    <limit effort="60" lower="0.0" upper="0.834" velocity="1.91986177778"/>
  </joint>
  
  <link name="robotiq_2f_85_right_driver">
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0.055"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="robotiq-2f-driver.obj" scale="0.1 0.1 0.1"/>
      </geometry>
      <material name="Blanc"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="robotiq-2f-driver.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  
  <!-- Right finger pad -->
  <joint name="robotiq_2f_85_right_pad_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.0315 0.0471"/>
    <parent link="robotiq_2f_85_right_driver"/>
    <child link="robotiq_2f_85_right_pad"/>
  </joint>
  
  <link name="robotiq_2f_85_right_pad">
    <inertial>
      <mass value="0.05"/>
      <origin xyz="0 0 0.02"/>
      <inertia ixx="0.00001" iyy="0.00001" izz="0.00001" ixy="0" iyz="0" ixz="0"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="robotiq-2f-pad.stl" scale="0.0001 0.0001 0.0001"/>
      </geometry>
      <material name="Blanc"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="robotiq-2f-pad.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  
  <!-- Left finger -->
  <joint name="robotiq_2f_85_left_driver_joint" type="revolute">
    <origin rpy="0 0 3.141592653589793" xyz="0 -0.0306011 0.054904"/>
    <parent link="robotiq_2f_85_base"/>
    <child link="robotiq_2f_85_left_driver"/>
    <axis xyz="1 0 0"/>
    <limit effort="60" lower="0.0" upper="0.834" velocity="1.91986177778"/>
    <mimic joint="robotiq_2f_85_right_driver_joint" multiplier="1"/>
  </joint>
  
  <link name="robotiq_2f_85_left_driver">
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0.055"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="robotiq-2f-driver.obj" scale="0.1 0.1 0.1"/>
      </geometry>
      <material name="Blanc"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="robotiq-2f-driver.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  
  <!-- Left finger pad -->
  <joint name="robotiq_2f_85_left_pad_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.0315 0.0471"/>
    <parent link="robotiq_2f_85_left_driver"/>
    <child link="robotiq_2f_85_left_pad"/>
  </joint>
  
  <link name="robotiq_2f_85_left_pad">
    <inertial>
      <mass value="0.05"/>
      <origin xyz="0 0 0.02"/>
      <inertia ixx="0.00001" iyy="0.00001" izz="0.00001" ixy="0" iyz="0" ixz="0"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="robotiq-2f-pad.stl" scale="0.0001 0.0001 0.0001"/>
      </geometry>
      <material name="Blanc"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="robotiq-2f-pad.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  
</robot>
