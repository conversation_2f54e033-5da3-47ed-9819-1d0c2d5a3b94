<?xml version="0.0" ?>
<robot name="spatula-base.urdf">
  <material name="DarkG<PERSON>">
    <color rgba="0.2 0.2 0.2 1.0"/>
  </material>
  <material name="<PERSON>Grey">
    <color rgba="0.8 0.8 0.8 1.0"/>
  </material>

  <link name="baseLink">
    <contact>
      <lateral_friction value="1.0"/>
      <rolling_friction value="0.0001"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="suction/base.obj" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="DarkG<PERSON>"/>
    </visual>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0.075"/>
      <geometry>
        <box size="0.05 0.005 0.11"/>
      </geometry>
      <material name="LightGrey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0.075"/>
      <geometry>
        <box size="0.05 0.005 0.11"/>
      </geometry>
    </collision>
  </link>

  <link name="midLink">
    <contact>
      <lateral_friction value="1.0"/>
      <rolling_friction value="0.0001"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0.1075"/>
      <geometry>
        <box size="0.05 0.005 0.005"/>
      </geometry>
      <material name="LightGrey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0.1075"/>
      <geometry>
        <box size="0.05 0.005 0.005"/>
      </geometry>
    </collision>
  </link>
  <joint name="baseLink-midLink" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.025"/>
    <parent link="baseLink"/>
    <child link="midLink"/>
  </joint>


</robot>

