<?xml version="0.0" ?>
<robot name="disk0.urdf">
  <link name="baseLink">
    <contact>
      <lateral_friction value="1.0"/>
      <rolling_friction value="0.0001"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
         <mesh filename="disk0.obj" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="red">
        <color rgba="1 0.3412 0.3490 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
	 	    <mesh filename="disk0.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
</robot>

