<?xml version="0.0" ?>
<robot name="corner.urdf">
  <link name="baseLink">
    <contact>
      <lateral_friction value="0.3"/>
      <rolling_friction value="0.0001"/>
      <inertia_scaling value="3.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>

    <visual>
      <origin rpy="0 0 0" xyz="DIMX0 DIMY0 0"/>
      <geometry>
        <box size=".055 .005 .001"/>
      </geometry>
      <material name="green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
    <!-- <collision>
      <origin rpy="0 0 0" xyz="0.025 0 0"/>
      <geometry>
        <box size=".055 .005 .001"/>
      </geometry>
    </collision> -->

    <visual>
      <origin rpy="0 0 0" xyz="DIMX1 DIMY1 0"/>
      <geometry>
        <box size=".005 .055 .001"/>
      </geometry>
      <material name="green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
    <!-- <collision>
      <origin rpy="0 0 0" xyz="0 0.025 0"/>
      <geometry>
        <box size=".005 .055 .001"/>
      </geometry>
    </collision> -->
  </link>
</robot>

