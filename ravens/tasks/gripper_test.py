# coding=utf-8
# Copyright 2021 The Ravens Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Test task for gripper functionality."""

import numpy as np
from ravens.tasks.task import Task
from ravens.tasks.grippers import RobotiqGripper
from ravens.utils import utils

import pybullet as p


class GripperTest(Task):
  """Simple test task for gripper functionality."""

  def __init__(self, *args, **kwargs):
    super().__init__(*args, **kwargs)
    self.max_steps = 3
    # Use gripper instead of suction
    self.ee = RobotiqGripper

  def reset(self, env):
    super().reset(env)

    # Add a simple block to pick up
    block_id = self.add_block(env)

    # Add a target zone to place the block
    target_pose = self.add_target_zone(env)

    # Set up the goal: pick up the block and place it in the target zone
    self.goals.append(([(block_id, (2 * np.pi, None))], np.int32([[1]]),
                       [target_pose], False, True, 'pose', None, 1))

  def add_block(self, env):
    """Add a simple block for the gripper to pick up."""
    size = (0.04, 0.04, 0.04)  # Small cube
    urdf = 'block/block.urdf'
    # Fixed position for easier testing
    pose = ((0.5, 0.1, 0.02), p.getQuaternionFromEuler((0, 0, 0)))
    return env.add_object(urdf, pose)

  def add_target_zone(self, env):
    """Add a target zone where the block should be placed."""
    # Fixed target position for easier testing
    pose = ((0.5, -0.1, 0.02), p.getQuaternionFromEuler((0, 0, 0)))
    return pose


class GripperPickPlace(Task):
  """Pick and place task using gripper."""

  def __init__(self, *args, **kwargs):
    super().__init__(*args, **kwargs)
    self.max_steps = 3
    # Use gripper instead of suction
    self.ee = RobotiqGripper

  def reset(self, env):
    super().reset(env)
    
    # Add multiple blocks
    block_ids = []
    for i in range(3):
      block_id = self.add_block(env)
      block_ids.append(block_id)
    
    # Add target zones
    target_poses = []
    for i in range(3):
      target_pose = self.get_random_pose(env, (0.04, 0.04, 0.04))
      target_poses.append(target_pose)
    
    # Set up goals for each block
    for i, block_id in enumerate(block_ids):
      self.goals.append(([(block_id, (2 * np.pi, None))], np.int32([[1]]),
                         [target_poses[i]], False, True, 'pose', None, 1/len(block_ids)))

  def add_block(self, env):
    """Add a block for the gripper to pick up."""
    size = (0.04, 0.04, 0.04)  # Small cube
    urdf = 'block/block.urdf'
    pose = self.get_random_pose(env, size)
    return env.add_object(urdf, pose)


class GripperBlockInsertion(Task):
  """Block insertion task using gripper instead of suction."""

  def __init__(self, *args, **kwargs):
    super().__init__(*args, **kwargs)
    self.max_steps = 3
    # Use gripper instead of suction
    self.ee = RobotiqGripper
    self.sixdof = True
    self.pos_eps = 0.02

  def reset(self, env):
    super().reset(env)
    block_id = self.add_block(env)
    targ_pose = self.add_fixture(env)
    self.goals.append(([(block_id, (2 * np.pi, None))], np.int32([[1]]),
                       [targ_pose], False, True, 'pose', None, 1))

  def add_block(self, env):
    """Add L-shaped block."""
    size = (0.1, 0.1, 0.04)
    urdf = 'insertion/ell.urdf'
    pose = self.get_random_pose(env, size)
    return env.add_object(urdf, pose)

  def add_fixture(self, env):
    """Add L-shaped fixture to place block."""
    size = (0.1, 0.1, 0.04)
    urdf = 'insertion/fixture.urdf'
    pose = self.get_random_pose_6dof(env, size)
    env.add_object(urdf, pose, 'fixed')
    return pose

  def get_random_pose_6dof(self, env, obj_size):
    """Get random 6DOF pose for the fixture."""
    pos, rot = super().get_random_pose(env, obj_size)
    z = 0.03
    pos = (pos[0], pos[1], obj_size[2] / 2 + z)
    roll = np.random.rand() * np.pi/3 - np.pi/6  # ±30 degrees
    pitch = np.random.rand() * np.pi/3 - np.pi/6  # ±30 degrees
    yaw = np.random.rand() * 2 * np.pi
    rot = utils.eulerXYZ_to_quatXYZW((roll, pitch, yaw))
    return pos, rot
