#!/usr/bin/env python3

"""Demo script showing gripper functionality in MIRA framework."""

import os
import sys
import time
import numpy as np

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

import pybullet as p
from ravens.tasks.grippers import RobotiqGripper
from ravens.utils import pybullet_utils


def demo_gripper_basic():
    """Basic demonstration of gripper functionality."""
    print("Starting basic gripper demonstration...")
    
    # Start PyBullet with GUI
    client = p.connect(p.GUI)
    p.setGravity(0, 0, -9.8)
    p.setRealTimeSimulation(0)
    
    # Set camera position for better view
    p.resetDebugVisualizerCamera(
        cameraDistance=1.5,
        cameraYaw=45,
        cameraPitch=-30,
        cameraTargetPosition=[0.5, 0, 0.2])
    
    try:
        assets_root = './ravens/environments/assets/'
        p.setAdditionalSearchPath(assets_root)
        
        # Load plane
        plane_id = p.loadURDF("plane/plane.urdf", [0, 0, -0.001])
        
        # Load UR5 robot
        ur5_id = p.loadURDF("ur5/ur5.urdf", [0, 0, 0])
        print(f"Loaded UR5 robot with ID: {ur5_id}")
        
        # Set robot to home position
        home_joints = np.array([-1, -0.5, 0.5, -0.5, -0.5, 0]) * np.pi
        for i, joint_pos in enumerate(home_joints):
            p.resetJointState(ur5_id, i, joint_pos)
        
        # Create mock obj_ids and add some objects
        obj_ids = {'rigid': [], 'fixed': [], 'deformable': []}
        
        # Add some blocks to interact with
        block_positions = [
            [0.5, 0.1, 0.02],
            [0.5, -0.1, 0.02],
            [0.6, 0.0, 0.02]
        ]
        
        for i, pos in enumerate(block_positions):
            block_id = p.loadURDF("block/block.urdf", pos)
            obj_ids['rigid'].append(block_id)
            # Color the blocks differently
            color = [1.0, 0.2 * i, 0.2 * (3-i), 1.0]
            p.changeVisualShape(block_id, -1, rgbaColor=color)
        
        print(f"Added {len(obj_ids['rigid'])} blocks")
        
        # Initialize gripper
        gripper = RobotiqGripper(assets_root, ur5_id, 9, obj_ids)
        print(f"Initialized gripper with body ID: {gripper.body}")
        
        # Demo sequence
        print("\nStarting demonstration sequence...")
        
        # Step 1: Show gripper opening and closing
        print("1. Demonstrating gripper opening and closing...")
        for cycle in range(3):
            print(f"  Cycle {cycle + 1}/3")
            
            # Close gripper
            gripper._close_gripper()
            for _ in range(60):  # 1 second at 60 Hz
                p.stepSimulation()
                time.sleep(1./60.)
            
            # Open gripper
            gripper._open_gripper()
            for _ in range(60):  # 1 second at 60 Hz
                p.stepSimulation()
                time.sleep(1./60.)
        
        # Step 2: Move robot closer to objects
        print("2. Moving robot to interact with objects...")
        
        # Define target joint positions to move end effector closer to blocks
        target_joints = np.array([-1.2, -1.0, 1.2, -1.7, -1.5, 0])
        
        # Smooth movement to target position
        current_joints = np.array([p.getJointState(ur5_id, i)[0] for i in range(6)])
        
        for step in range(120):  # 2 seconds of movement
            alpha = step / 119.0  # Interpolation factor
            interpolated_joints = current_joints + alpha * (target_joints - current_joints)
            
            for i, joint_pos in enumerate(interpolated_joints):
                p.setJointMotorControl2(
                    ur5_id, i, p.POSITION_CONTROL, 
                    targetPosition=joint_pos, force=500)
            
            p.stepSimulation()
            time.sleep(1./60.)
        
        # Step 3: Attempt to grasp an object
        print("3. Attempting to grasp an object...")
        
        # Close gripper and try to activate
        gripper._close_gripper()
        for _ in range(30):
            p.stepSimulation()
            time.sleep(1./60.)
        
        # Try to activate gripper (create constraints if in contact)
        gripper.activate()
        print(f"   Gripper activated: {gripper.activated}")
        
        if gripper.activated:
            print("   Successfully grasped an object!")
            
            # Hold for a moment
            for _ in range(60):
                p.stepSimulation()
                time.sleep(1./60.)
            
            # Release the object
            print("   Releasing object...")
            gripper.release()
            print(f"   Gripper activated after release: {gripper.activated}")
        else:
            print("   No object grasped (this is normal if not in contact)")
        
        # Step 4: Final demonstration
        print("4. Final gripper movements...")
        for _ in range(3):
            gripper._close_gripper()
            for _ in range(30):
                p.stepSimulation()
                time.sleep(1./60.)
            
            gripper._open_gripper()
            for _ in range(30):
                p.stepSimulation()
                time.sleep(1./60.)
        
        print("\nDemonstration completed successfully!")
        print("Press Enter to close the simulation...")
        input()
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        p.disconnect()


def demo_gripper_comparison():
    """Compare gripper and suction end effectors."""
    print("Gripper vs Suction Comparison Demo")
    print("This demo shows the difference between gripper and suction end effectors")
    
    # This would require more complex setup, so for now just print info
    print("\nGripper characteristics:")
    print("- Uses finger-based grasping")
    print("- Can grasp objects from the sides")
    print("- Requires proper finger positioning")
    print("- Good for objects with graspable features")
    
    print("\nSuction characteristics:")
    print("- Uses vacuum-based attachment")
    print("- Requires flat surfaces for attachment")
    print("- Works well with smooth, flat objects")
    print("- Simpler control (on/off)")
    
    print("\nFor a full comparison, run the basic demo to see gripper in action!")


if __name__ == '__main__':
    print("MIRA Gripper Demonstration")
    print("=" * 50)
    
    print("\nAvailable demos:")
    print("1. Basic gripper functionality")
    print("2. Gripper vs Suction comparison (info only)")
    
    choice = input("\nSelect demo (1 or 2, or Enter for default): ").strip()
    
    if choice == "2":
        demo_gripper_comparison()
    else:
        print("\nStarting basic gripper demo...")
        print("Note: This will open a PyBullet GUI window")
        print("Close the window or press Ctrl+C to exit")
        
        try:
            demo_gripper_basic()
        except KeyboardInterrupt:
            print("\nDemo interrupted by user")
        except Exception as e:
            print(f"Demo failed: {e}")
    
    print("\nDemo completed!")
