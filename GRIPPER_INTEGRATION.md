# MIRA 夹爪集成文档

## 概述

本文档描述了在 MIRA (Mental Imagery for Robotic Affordances) 框架中集成 Robotiq 2F-85 夹爪的实现。MIRA 原本主要使用吸盘作为末端执行器，现在已扩展支持夹爪进行物体操纵任务。

## 实现的功能

### 1. 夹爪类实现 (`RobotiqGripper`)

**位置**: `ravens/tasks/grippers.py`

**主要功能**:
- 基于 Robotiq 2F-85 夹爪的 PyBullet 仿真
- 手指开合控制
- 物体抓取和释放
- 接触检测
- 抓取状态检查

**关键方法**:
- `activate()`: 激活夹爪，尝试抓取物体
- `release()`: 释放夹爪，松开物体
- `_open_gripper()`: 打开夹爪手指
- `_close_gripper()`: 关闭夹爪手指
- `detect_contact()`: 检测与物体的接触
- `check_grasp()`: 检查是否成功抓取物体

### 2. URDF 文件

**原始文件**: `ravens/environments/assets/ur5/gripper/robotiq_2f_85.urdf`
**优化文件**: `ravens/environments/assets/ur5/gripper/robotiq_2f_85_mira.urdf`

优化的 URDF 文件特点:
- 简化的关节结构
- 优化的质量和惯性参数
- 启用碰撞检测
- 支持 mimic 关节（左右手指同步）

### 3. 测试任务

**位置**: `ravens/tasks/gripper_test.py`

实现了三个测试任务:
- `GripperTest`: 基本夹爪功能测试
- `GripperPickPlace`: 夹爪拾取放置任务
- `GripperBlockInsertion`: 使用夹爪的块插入任务

### 4. 任务注册

**位置**: `ravens/tasks/__init__.py`

新增任务名称:
- `'gripper-test'`: 基本夹爪测试
- `'gripper-pick-place'`: 夹爪拾取放置
- `'gripper-block-insertion'`: 夹爪块插入

## 技术实现细节

### 夹爪控制机制

1. **关节控制**: 使用 PyBullet 的 `setJointMotorControl2` 控制手指关节
2. **约束创建**: 通过 `createConstraint` 实现物体抓取
3. **接触检测**: 使用 `getContactPoints` 检测夹爪与物体的接触
4. **状态管理**: 维护夹爪的激活状态和约束引用

### 与现有框架的集成

1. **继承结构**: `RobotiqGripper` 继承自基类 `Gripper`
2. **接口兼容**: 实现与 `Suction` 类相同的接口方法
3. **环境集成**: 通过任务的 `self.ee` 属性指定末端执行器类型
4. **Oracle 支持**: 兼容现有的 Oracle 代理系统

## 文件结构

```
ravens/
├── tasks/
│   ├── grippers.py          # 夹爪类实现
│   ├── gripper_test.py      # 测试任务
│   └── __init__.py          # 任务注册
├── environments/
│   └── assets/
│       └── ur5/
│           └── gripper/
│               ├── robotiq_2f_85_mira.urdf  # 优化的 URDF
│               ├── robotiq-2f-base.obj      # 网格文件
│               ├── robotiq-2f-driver.obj
│               └── robotiq-2f-pad.stl
└── ...

# 测试和演示文件
test_gripper_simple.py       # 简单功能测试
demo_gripper.py             # 交互式演示
GRIPPER_INTEGRATION.md      # 本文档
```

## 使用方法

### 1. 在任务中使用夹爪

```python
from ravens.tasks.grippers import RobotiqGripper
from ravens.tasks.task import Task

class MyGripperTask(Task):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 使用夹爪而不是吸盘
        self.ee = RobotiqGripper
```

### 2. 运行测试

```bash
# 基本功能测试
python3 test_gripper_simple.py

# 交互式演示（需要 GUI）
python3 demo_gripper.py
```

### 3. 使用预定义任务

```bash
# 使用夹爪测试任务生成数据
python ravens/demos.py --assets_root=./ravens/environments/assets/ --task=gripper-test --mode=train --n=10
```

## 测试结果

### 基本功能测试
- ✅ 文件结构检查
- ✅ URDF 加载测试
- ✅ 夹爪类实例化
- ✅ 关节控制功能
- ✅ 抓取/释放逻辑
- ✅ 接触检测
- ✅ 状态管理

### 集成测试
- ✅ 与 UR5 机器人集成
- ✅ PyBullet 环境兼容性
- ✅ 任务系统集成
- ✅ Oracle 代理兼容性

## 与吸盘的对比

| 特性 | 夹爪 (Gripper) | 吸盘 (Suction) |
|------|----------------|----------------|
| 抓取方式 | 手指夹持 | 真空吸附 |
| 适用物体 | 有抓取特征的物体 | 平滑表面物体 |
| 控制复杂度 | 较高（多关节） | 较低（开/关） |
| 抓取精度 | 高 | 中等 |
| 适用场景 | 精密操作 | 平面物体搬运 |

## 后续扩展建议

1. **力控制**: 实现基于力反馈的抓取控制
2. **多指夹爪**: 支持更复杂的多指夹爪
3. **自适应抓取**: 根据物体形状自动调整抓取策略
4. **碰撞避免**: 改进夹爪的碰撞检测和避免
5. **真实机器人**: 扩展到真实 Robotiq 夹爪的控制

## 已知限制

1. **简化物理**: 当前实现使用约束模拟抓取，未考虑复杂的接触力学
2. **固定参数**: 夹爪参数（力、速度等）是固定的，未实现自适应调整
3. **有限测试**: 主要在简单几何体上测试，复杂物体可能需要调整
4. **视觉依赖**: 依赖现有的视觉系统，未针对夹爪优化

## 总结

成功在 MIRA 框架中集成了 Robotiq 2F-85 夹爪，实现了：
- 完整的夹爪仿真和控制
- 与现有框架的无缝集成
- 多个测试任务和演示
- 详细的文档和测试

这为 MIRA 框架扩展到更多样化的机器人操纵任务奠定了基础。
